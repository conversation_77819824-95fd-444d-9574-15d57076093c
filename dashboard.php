<?php
session_start();

// Verificar autenticación
if (!($_SESSION['authenticated'] ?? false)) {
    header('Location: index.php');
    exit;
}

// Verificar timeout de sesión (30 minutos)
if (time() - ($_SESSION['login_time'] ?? 0) > 1800) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// Procesar logout
if ($_GET['logout'] ?? false) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// ========================================
// FUNCIONES DE MONITOREO
// ========================================

function getRecentAttempts() {
    $attempts = [];
    $files = glob('login_attempts_*.txt');
    
    foreach ($files as $file) {
        if (file_exists($file)) {
            $data = json_decode(file_get_contents($file), true);
            if ($data['attempts'] > 0) {
                $ip = str_replace(['login_attempts_', '.txt'], '', basename($file));
                $attempts[] = [
                    'ip_hash' => $ip,
                    'attempts' => $data['attempts'],
                    'locked_until' => $data['locked_until']
                ];
            }
        }
    }
    
    return $attempts;
}

function getRateLimitData() {
    $data = [];
    $files = glob('rate_limit_*.txt');
    
    foreach ($files as $file) {
        if (file_exists($file)) {
            $content = json_decode(file_get_contents($file), true);
            if (!empty($content['attempts'])) {
                $ip = str_replace(['rate_limit_', '.txt'], '', basename($file));
                $data[] = [
                    'ip_hash' => $ip,
                    'requests' => count($content['attempts']),
                    'last_request' => max($content['attempts'])
                ];
            }
        }
    }
    
    return $data;
}

function cleanOldFiles() {
    $files = array_merge(glob('login_attempts_*.txt'), glob('rate_limit_*.txt'));
    $cleaned = 0;
    
    foreach ($files as $file) {
        if (filemtime($file) < time() - 86400) { // Más de 24 horas
            unlink($file);
            $cleaned++;
        }
    }
    
    return $cleaned;
}

// Limpiar archivos antiguos si se solicita
if ($_GET['clean'] ?? false) {
    $cleaned = cleanOldFiles();
    $message = "Se limpiaron $cleaned archivos antiguos.";
}

$recentAttempts = getRecentAttempts();
$rateLimitData = getRateLimitData();

// Función para mostrar el log de accesos
function getSecurityLogArray() {
    $logFile = 'security_log.txt';
    $logs = [];
    if (file_exists($logFile)) {
        $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach (array_reverse($lines) as $line) {
            // Separar los datos del log
            $parts = explode('|', $line);
            $logs[] = array_map('trim', $parts);
        }
    }
    return $logs;
}

// Manejo de bloqueo/desbloqueo de IPs
$blockFile = 'blocked_ips.txt';
if (isset($_POST['block_ip'])) {
    $ipToBlock = $_POST['block_ip'];
    file_put_contents($blockFile, $ipToBlock."\n", FILE_APPEND);
    header('Location: dashboard.php');
    exit;
}
if (isset($_POST['unblock_ip'])) {
    $ipToUnblock = $_POST['unblock_ip'];
    if (file_exists($blockFile)) {
        $ips = file($blockFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $ips = array_diff($ips, [$ipToUnblock]);
        file_put_contents($blockFile, implode("\n", $ips));
    }
    header('Location: dashboard.php');
    exit;
}
function isBlocked($ip) {
    global $blockFile;
    if (!file_exists($blockFile)) return false;
    $ips = file($blockFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    return in_array($ip, $ips);
}

// Nueva función para mostrar el log de accesos
function getSecurityLog() {
    $logFile = 'security_log.txt';
    if (file_exists($logFile)) {
        return file_get_contents($logFile);
    }
    return '';
}

// Si la petición es AJAX para el log, solo devuelve el log
if (isset($_GET['getlog'])) {
    echo getSecurityLog();
    exit;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Control - Sistema Seguro</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .header { 
            background: #007cba; 
            color: white; 
            padding: 20px; 
            border-radius: 10px; 
            margin-bottom: 20px; 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
        }
        .card { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            margin-bottom: 20px; 
        }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin-bottom: 20px; 
        }
        .stat-card { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 20px; 
            border-radius: 10px; 
            text-align: center; 
        }
        .stat-number { 
            font-size: 2em; 
            font-weight: bold; 
            margin-bottom: 10px; 
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 15px; 
        }
        th, td { 
            padding: 12px; 
            text-align: left; 
            border-bottom: 1px solid #ddd; 
        }
        th { 
            background: #f8f9fa; 
            font-weight: bold; 
        }
        .btn { 
            background: #007cba; 
            color: white; 
            padding: 10px 20px; 
            text-decoration: none; 
            border-radius: 5px; 
            display: inline-block; 
            margin: 5px; 
        }
        .btn:hover { 
            background: #005a87; 
        }
        .btn-danger { 
            background: #dc3545; 
        }
        .btn-danger:hover { 
            background: #c82333; 
        }
        .alert { 
            padding: 15px; 
            margin-bottom: 20px; 
            border-radius: 5px; 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        .status-locked { 
            color: #dc3545; 
            font-weight: bold; 
        }
        .status-active { 
            color: #28a745; 
            font-weight: bold; 
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Panel de Control de Seguridad</h1>
        <div>
            <span>Sesión activa desde: <?php echo date('H:i:s', $_SESSION['login_time']); ?></span>
            <a href="?logout=1" class="btn btn-danger" style="margin-left: 20px;">Cerrar Sesión</a>
        </div>
    </div>

    <?php if (isset($message)): ?>
        <div class="alert"><?php echo htmlspecialchars($message); ?></div>
    <?php endif; ?>

    <div class="stats">
        <div class="stat-card">
            <div class="stat-number"><?php echo count($recentAttempts); ?></div>
            <div>IPs con Intentos Fallidos</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo count($rateLimitData); ?></div>
            <div>IPs Monitoreadas</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo date('H:i:s'); ?></div>
            <div>Hora del Servidor</div>
        </div>
    </div>

    <div class="card">
        <h2>Intentos de Login Fallidos</h2>
        <p>Monitoreo de IPs que han intentado acceder sin éxito:</p>
        
        <?php if (empty($recentAttempts)): ?>
            <p style="color: #28a745; font-weight: bold;">✓ No hay intentos fallidos recientes</p>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>IP (Hash)</th>
                        <th>Intentos</th>
                        <th>Estado</th>
                        <th>Bloqueado hasta</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentAttempts as $attempt): ?>
                        <tr>
                            <td><?php echo substr($attempt['ip_hash'], 0, 16) . '...'; ?></td>
                            <td><?php echo $attempt['attempts']; ?></td>
                            <td>
                                <?php if ($attempt['locked_until'] > time()): ?>
                                    <span class="status-locked">BLOQUEADA</span>
                                <?php else: ?>
                                    <span class="status-active">ACTIVA</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php 
                                if ($attempt['locked_until'] > time()) {
                                    echo date('H:i:s', $attempt['locked_until']);
                                } else {
                                    echo '-';
                                }
                                ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <div class="card">
        <h2>Actividad de Rate Limiting</h2>
        <p>IPs que han realizado múltiples solicitudes:</p>
        
        <?php if (empty($rateLimitData)): ?>
            <p style="color: #28a745; font-weight: bold;">✓ No hay actividad sospechosa</p>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>IP (Hash)</th>
                        <th>Solicitudes (última hora)</th>
                        <th>Última solicitud</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($rateLimitData as $data): ?>
                        <tr>
                            <td><?php echo substr($data['ip_hash'], 0, 16) . '...'; ?></td>
                            <td><?php echo $data['requests']; ?></td>
                            <td><?php echo date('H:i:s', $data['last_request']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <div class="card">
        <h2>Acciones de Mantenimiento</h2>
        <p>Herramientas para mantener el sistema limpio y seguro:</p>
        
        <a href="?clean=1" class="btn">Limpiar Archivos Antiguos</a>
        <a href="security_log.php" class="btn">Ver Log de Seguridad</a>
        <a href="?refresh=1" class="btn">Actualizar Datos</a>
    </div>

    <div class="card">
        <h2>Estado del Sistema</h2>
        <ul>
            <li><strong>Protección .htaccess:</strong> ✓ Activa</li>
            <li><strong>Detección de bots:</strong> ✓ Activa</li>
            <li><strong>Rate limiting:</strong> ✓ Activa</li>
            <li><strong>Sistema de login:</strong> ✓ Activa</li>
            <li><strong>Timeout de sesión:</strong> ✓ 30 minutos</li>
            <li><strong>Bloqueo por intentos:</strong> ✓ 3 intentos / 30 min</li>
        </ul>
    </div>

    <div class="card">
        <h2>Tráfico y Accesos en Tiempo Real</h2>
        <table style="width:100%;font-size:13px;">
            <thead>
                <tr>
                    <th>Fecha</th>
                    <th>IP</th>
                    <th>ISP</th>
                    <th>User Agent</th>
                    <th>Ruta</th>
                    <th>Archivo</th>
                    <th>Acción</th>
                </tr>
            </thead>
            <tbody id="security-log-body">
                <?php foreach (getSecurityLogArray() as $log):
                    $ip = isset($log[1]) ? str_replace('IP: ', '', $log[1]) : '';
                ?>
                <tr>
                    <td><?php echo htmlspecialchars($log[0] ?? ''); ?></td>
                    <td><?php echo htmlspecialchars($ip); ?></td>
                    <td><?php echo htmlspecialchars(isset($log[2]) ? str_replace('ISP: ', '', $log[2]) : ''); ?></td>
                    <td><?php echo htmlspecialchars(isset($log[3]) ? str_replace('UA: ', '', $log[3]) : ''); ?></td>
                    <td><?php echo htmlspecialchars(isset($log[4]) ? str_replace('URI: ', '', $log[4]) : ''); ?></td>
                    <td><?php echo htmlspecialchars(isset($log[5]) ? str_replace('SCRIPT: ', '', $log[5]) : ''); ?></td>
                    <td>
                        <?php if ($ip && !isBlocked($ip)): ?>
                        <form method="POST" style="display:inline;">
                            <input type="hidden" name="block_ip" value="<?php echo htmlspecialchars($ip); ?>">
                            <button type="submit" style="background:#dc3545;color:#fff;border:none;padding:5px 10px;border-radius:3px;cursor:pointer;">Bloquear</button>
                        </form>
                        <?php elseif ($ip): ?>
                        <form method="POST" style="display:inline;">
                            <input type="hidden" name="unblock_ip" value="<?php echo htmlspecialchars($ip); ?>">
                            <button type="submit" style="background:#28a745;color:#fff;border:none;padding:5px 10px;border-radius:3px;cursor:pointer;">Desbloquear</button>
                        </form>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <script>
        function updateLog() {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'dashboard.php?getlog=1', true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    document.getElementById('security-log').textContent = xhr.responseText;
                }
            };
            xhr.send();
        }
        setInterval(updateLog, 5000); // Actualiza cada 5 segundos
        window.onload = updateLog;
    </script>

    <script>
        // Auto-refresh cada 30 segundos
        setTimeout(function() {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
