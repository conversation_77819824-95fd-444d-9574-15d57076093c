# ========================================
# PROTECCIÓN AVANZADA CONTRA VULNERABILIDADES
# ========================================

# Deshabilitar listado de directorios
Options -Indexes

# Proteger archivos sensibles
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql|conf|config)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Bloquear acceso a archivos de configuración
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# ========================================
# PROTECCIÓN CONTRA BOTS Y SCRAPERS
# ========================================

# Bloquear User Agents maliciosos conocidos
RewriteEngine On
RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
RewriteCond %{HTTP_USER_AGENT} (libwww-perl|wget|python|nikto|curl|scan|java|winhttp|clshttp|loader) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (;|<|>|'|"|\)|\(|%0A|%0D|%27|%3C|%3E|%00) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (Scrapy|scrapy|bot|Bot|BOT|spider|Spider|SPIDER) [NC]
RewriteRule .* - [F,L]

# Bloquear IPs sospechosas (añade las IPs que detectes como maliciosas)
# RewriteCond %{REMOTE_ADDR} ^123\.456\.789\.000$
# RewriteRule .* - [F,L]

# ========================================
# PROTECCIÓN CONTRA ATAQUES COMUNES
# ========================================

# Proteger contra inyección SQL
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} concat.*\( [NC,OR]
RewriteCond %{QUERY_STRING} union.*select.*\( [NC,OR]
RewriteCond %{QUERY_STRING} union.*all.*select.* [NC,OR]
RewriteCond %{QUERY_STRING} \-[sdcr].*(allow_url_include|allow_url_fopen|safe_mode|disable_functions|auto_prepend_file) [NC]
RewriteRule .* - [F,L]

# Proteger contra XSS
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC]
RewriteRule .* - [F,L]

# ========================================
# LIMITACIÓN DE VELOCIDAD Y ACCESOS
# ========================================

# Limitar tamaño de archivos subidos
LimitRequestBody 10485760

# Timeout de conexión
Timeout 300

# ========================================
# HEADERS DE SEGURIDAD
# ========================================

<IfModule mod_headers.c>
    # Prevenir clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Protección XSS
    Header set X-XSS-Protection "1; mode=block"
    
    # Prevenir MIME sniffing
    Header set X-Content-Type-Options nosniff
    
    # Política de referrer
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy básica
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
    
    # Ocultar información del servidor
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# ========================================
# PROTECCIÓN DE DIRECTORIOS ESPECÍFICOS
# ========================================

# Bloquear acceso a directorios admin, config, etc.
RewriteRule ^(admin|config|includes|logs|backup|private)/ - [F,L]

# Redireccionar intentos de acceso a archivos PHP directamente
RewriteCond %{THE_REQUEST} /([^/]+)\.php[\s?] [NC]
RewriteRule ^([^/]+)\.php$ /$1 [R=301,L]

# ========================================
# LOGGING DE SEGURIDAD
# ========================================

# Log de intentos de acceso a archivos prohibidos
RewriteCond %{REQUEST_URI} \.(htaccess|htpasswd|ini|log|sh|inc|bak|sql|conf|config)$ [NC]
RewriteRule .* - [E=SECURITY_ALERT:prohibited_file_access,L]

# Log de intentos de inyección SQL
RewriteCond %{QUERY_STRING} (union.*select|concat.*\(|script.*\>|<.*script) [NC]
RewriteRule .* - [E=SECURITY_ALERT:sql_injection_attempt,L]

# ========================================
# BLOQUEO DE PAÍSES ESPECÍFICOS (OPCIONAL)
# ========================================

# Descomenta y ajusta según necesites
# RewriteCond %{ENV:GEOIP_COUNTRY_CODE} ^(CN|RU|KP)$
# RewriteRule .* - [F,L]

# ========================================
# PROTECCIÓN ADICIONAL CONTRA EXPLOITS
# ========================================

# Bloquear intentos de directory traversal
RewriteCond %{QUERY_STRING} \.\./\.\./\.\./
RewriteRule .* - [F,L]

# Bloquear intentos de acceso a archivos de sistema
RewriteCond %{REQUEST_URI} (etc/passwd|proc/|dev/|sys/) [NC]
RewriteRule .* - [F,L]

# Bloquear métodos HTTP peligrosos
RewriteCond %{REQUEST_METHOD} ^(TRACE|DELETE|TRACK) [NC]
RewriteRule .* - [F,L]
