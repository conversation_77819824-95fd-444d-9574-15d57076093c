<?php
session_start();

// --- CONFIGURACIÓN ---
$logFile = 'security_log.txt';
$blockFile = 'blocked_ips.txt';
$adminIP = 'TU_IP_AQUI'; // Cambia esto por tu IP real para nunca bloquearte

// --- REGISTRO DE ACCESOS ---
$ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
$date = date('Y-m-d H:i:s');
$requestUri = $_SERVER['REQUEST_URI'] ?? '';
$scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
$isp = '';
if ($ip !== 'unknown') {
    $ispApi = @file_get_contents("https://ipapi.co/{$ip}/org/");
    if ($ispApi !== false) {
        $isp = trim($ispApi);
    }
}
$logLine = "$date | IP: $ip | ISP: $isp | UA: $userAgent | URI: $requestUri | SCRIPT: $scriptName\n";
file_put_contents($logFile, $logLine, FILE_APPEND);

// --- BLOQUEO DE IPs ---
if ($ip !== $adminIP && $ip !== 'unknown' && file_exists($blockFile)) {
    $blockedIps = file($blockFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if (in_array($ip, $blockedIps)) {
        http_response_code(403);
        die('Acceso bloqueado por el administrador');
    }
}

// --- PANEL DE CONTROL ---
if (isset($_GET['panel']) && $ip === $adminIP) {
    // Mostrar panel visual de accesos y bloqueo
    echo '<!DOCTYPE html><html lang="es"><head><meta charset="UTF-8"><title>Panel de Seguridad</title>';
    echo '<style>body{font-family:Arial;background:#f0f0f0;}table{width:100%;border-collapse:collapse;}th,td{padding:8px;border:1px solid #ccc;}th{background:#007cba;color:#fff;}tr:nth-child(even){background:#f9f9f9;}button{padding:5px 10px;}</style>';
    echo '</head><body><h2>Panel de Seguridad</h2>';
    // Mostrar accesos
    echo '<h3>Accesos recientes</h3><table><tr><th>Fecha</th><th>IP</th><th>ISP</th><th>User Agent</th><th>Ruta</th><th>Archivo</th><th>Acción</th></tr>';
    $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach (array_reverse($lines) as $line) {
        $parts = explode('|', $line);
        $ipLog = isset($parts[1]) ? trim(str_replace('IP:','',$parts[1])) : '';
        echo '<tr>';
        foreach ($parts as $p) echo '<td>'.htmlspecialchars(trim($p)).'</td>';
        echo '<td>';
        if ($ipLog && $ipLog !== $adminIP) {
            echo '<form method="POST" style="display:inline;"><input type="hidden" name="block_ip" value="'.$ipLog.'"><button type="submit">Bloquear</button></form>';
            echo '<form method="POST" style="display:inline;"><input type="hidden" name="unblock_ip" value="'.$ipLog.'"><button type="submit">Desbloquear</button></form>';
        }
        echo '</td></tr>';
    }
    echo '</table>';
    // Mostrar IPs bloqueadas
    echo '<h3>IPs bloqueadas</h3><ul>';
    if (file_exists($blockFile)) {
        foreach (file($blockFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES) as $bip) {
            echo '<li>'.htmlspecialchars($bip).'</li>';
        }
    }
    echo '</ul>';
    echo '</body></html>';
    exit;
}

// --- ACCIONES DE BLOQUEO/DESBLOQUEO ---
if ($ip === $adminIP && $_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['block_ip'])) {
        file_put_contents($blockFile, $_POST['block_ip']."\n", FILE_APPEND);
    }
    if (isset($_POST['unblock_ip'])) {
        if (file_exists($blockFile)) {
            $ips = file($blockFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $ips = array_diff($ips, [$_POST['unblock_ip']]);
            file_put_contents($blockFile, implode("\n", $ips));
        }
    }
    header('Location: ?panel=1');
    exit;
}

// --- PÁGINA PRINCIPAL ---
echo '<!DOCTYPE html><html lang="es"><head><meta charset="UTF-8"><title>Acceso Seguro</title>';
echo '<style>body{font-family:Arial;background:#f0f0f0;display:flex;justify-content:center;align-items:center;height:100vh;}';
echo '.login-container{background:#fff;padding:30px;border-radius:10px;box-shadow:0 0 20px rgba(0,0,0,0.1);max-width:400px;width:100%;}</style>';
echo '</head><body><div class="login-container">';
echo '<h2>Acceso Seguro al Sistema</h2>';
echo '<form method="POST"><div class="form-group"><label for="username">Usuario:</label><input type="text" id="username" name="username" required></div>';
echo '<div class="form-group"><label for="password">Contraseña:</label><input type="password" id="password" name="password" required></div>';
echo '<button type="submit" name="login">Acceder</button></form>';
echo '<p style="text-align:center;margin-top:20px;font-size:12px;color:#666;">Sistema protegido contra accesos no autorizados</p>';
echo '<p style="text-align:center;margin-top:10px;"><a href="?panel=1">Ir al Panel de Seguridad</a></p>';
echo '</div></body></html>';
exit;

// ========================================
// SISTEMA DE RATE LIMITING
// ========================================

function checkRateLimit() {
    $ip = $_SERVER['REMOTE_ADDR'];
    $file = 'rate_limit_' . md5($ip) . '.txt';
    
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
        $now = time();
        
        // Limpiar intentos antiguos (más de 1 hora)
        $data['attempts'] = array_filter($data['attempts'], function($timestamp) use ($now) {
            return ($now - $timestamp) < 3600;
        });
        
        // Si hay más de 10 intentos en la última hora, bloquear
        if (count($data['attempts']) > 10) {
            return false;
        }
        
        $data['attempts'][] = $now;
    } else {
        $data = ['attempts' => [time()]];
    }
    
    file_put_contents($file, json_encode($data));
    return true;
}

// ========================================
// SISTEMA DE AUTENTICACIÓN
// ========================================

function checkLoginAttempts() {
    $ip = $_SERVER['REMOTE_ADDR'];
    $file = 'login_attempts_' . md5($ip) . '.txt';
    
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
        
        if ($data['locked_until'] > time()) {
            return false;
        }
        
        if ($data['attempts'] >= MAX_LOGIN_ATTEMPTS) {
            $data['locked_until'] = time() + LOCKOUT_TIME;
            file_put_contents($file, json_encode($data));
            return false;
        }
    }
    
    return true;
}

function recordLoginAttempt($success = false) {
    $ip = $_SERVER['REMOTE_ADDR'];
    $file = 'login_attempts_' . md5($ip) . '.txt';
    
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
    } else {
        $data = ['attempts' => 0, 'locked_until' => 0];
    }
    
    if ($success) {
        $data['attempts'] = 0;
        $data['locked_until'] = 0;
    } else {
        $data['attempts']++;
    }
    
    file_put_contents($file, json_encode($data));
}

// ========================================
// VERIFICACIONES DE SEGURIDAD
// ========================================

// Verificar si es un bot
if (detectBot()) {
    http_response_code(403);
    die('Acceso denegado');
}

// Verificar rate limiting
if (!checkRateLimit()) {
    http_response_code(429);
    die('Demasiadas solicitudes. Intenta más tarde.');
}

// Verificar intentos de login
if (!checkLoginAttempts()) {
    http_response_code(423);
    die('IP bloqueada temporalmente por múltiples intentos fallidos.');
}

// ========================================
// PROCESAMIENTO DE LOGIN
// ========================================

$error = '';
$showCaptcha = false;

if ($_POST['login'] ?? false) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $captcha = $_POST['captcha'] ?? '';
    
    // Verificar CAPTCHA simple
    if ($_SESSION['captcha'] !== $captcha) {
        $error = 'CAPTCHA incorrecto';
        recordLoginAttempt(false);
    } elseif ($username === ADMIN_USERNAME && $password === ADMIN_PASSWORD) {
        $_SESSION['authenticated'] = true;
        $_SESSION['login_time'] = time();
        recordLoginAttempt(true);
        header('Location: dashboard.php');
        exit;
    } else {
        $error = 'Credenciales incorrectas';
        recordLoginAttempt(false);
    }
    
    $showCaptcha = true;
}

// Generar CAPTCHA simple
$num1 = rand(1, 10);
$num2 = rand(1, 10);
$_SESSION['captcha'] = $num1 + $num2;

// ========================================
// VERIFICAR SI YA ESTÁ AUTENTICADO
// ========================================

if ($_SESSION['authenticated'] ?? false) {
    // Verificar timeout de sesión (30 minutos)
    if (time() - ($_SESSION['login_time'] ?? 0) > 1800) {
        session_destroy();
        header('Location: index.php');
        exit;
    }
    
    header('Location: dashboard.php');
    exit;
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acceso Seguro</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: #f0f0f0; 
            display: flex; 
            justify-content: center; 
            align-items: center; 
            height: 100vh; 
            margin: 0; 
        }
        .login-container { 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 0 20px rgba(0,0,0,0.1); 
            max-width: 400px; 
            width: 100%; 
        }
        .form-group { 
            margin-bottom: 15px; 
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold; 
        }
        input[type="text"], input[type="password"] { 
            width: 100%; 
            padding: 10px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            box-sizing: border-box; 
        }
        button { 
            width: 100%; 
            padding: 12px; 
            background: #007cba; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px; 
        }
        button:hover { 
            background: #005a87; 
        }
        .error { 
            color: red; 
            margin-bottom: 15px; 
            text-align: center; 
        }
        .captcha { 
            background: #f9f9f9; 
            padding: 10px; 
            border-radius: 5px; 
            text-align: center; 
            margin-bottom: 15px; 
            font-weight: bold; 
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>Acceso Seguro al Sistema</h2>
        
        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">Usuario:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Contraseña:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="captcha">
                Resuelve: <?php echo $num1; ?> + <?php echo $num2; ?> = ?
            </div>
            
            <div class="form-group">
                <label for="captcha">Resultado:</label>
                <input type="text" id="captcha" name="captcha" required>
            </div>
            
            <button type="submit" name="login">Acceder</button>
        </form>
        
        <p style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
            Sistema protegido contra accesos no autorizados
        </p>
    </div>
</body>
</html>
